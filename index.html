<!DOCTYPE html>
<html lang="en">
  <head>
    <title><PERSON><PERSON><PERSON></title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="styles/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif:ital,wght@0,100..900;1,100..900&family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Russo+One&display=swap" rel="stylesheet">
    <script src="https://kit.fontawesome.com/91c7a109fb.js" crossorigin="anonymous"></script>
    <script src="script/script.js" defer></script>
  </head>
  <body>

    <!-- navbar -->
    <nav class="main-nav" id="mainNav">
      <button type="button" class="nav-toggle" onclick="toggleMenu()" aria-label="Toggle navigation menu">
        <i class="fa-solid fa-bars fa-lg"></i>
      </button>
      <ul class="nav-list">
        <li><a class="nav-link" onclick="closeMenu()" href="#profile">Profile</a></li>
        <li><a class="nav-link" onclick="closeMenu()" href="#skills">Skills</a></li>
        <li><a class="nav-link" onclick="closeMenu()" href="#education">Education</a></li>
        <li><a class="nav-link" onclick="closeMenu()" href="#projects">Projects</a></li>
      </ul>
    </nav>

    <!-- container for the rest of content -->
    <main class="container-body">

      <!-- main container for image and title -->
      <div class="main-container">
        <div class="bracket-left">/</div>
        
        <!-- container for image -->
        <div class="photo-frame">
          <div class="photo-container">
              <img src="images/image.png" alt="Portrait" class="photo">
          </div>
        </div>

        <!-- container for title -->
        <div class="title-container">
          <h2 class="title">FULLSTACK DEVELOPER</h2>
          <h1 class="name">YAROSLAV</h1>
          <h1 class="name">ROMASHKO</h1>
          <a href="mailto:<EMAIL>" class="desktop-button contact">
            <i class="fa-solid fa-envelope"></i>Send Email
          </a>
        </div>

        <div class="bracket-right">/</div>
        <a href="mailto:<EMAIL>" class="mobile-button contact">
          <i class="fa-solid fa-envelope"></i>Send Email
        </a>
      </div>

      <!-- container for profile information -->
      <section id="profile" class="profile">
        <h2>Profile</h2>
        <p>
          I spent six years working for an IT association back home, where I managed marketing initiatives 
          and led B2B sales efforts to bring new companies into our membership. This role built 
          upon my university degree in management and marketing.
        </p>
        <br>
        <p>
          Now based in Finland, I'm making an exciting career transition into software development. 
          I'm thoroughly passionate about programming and problem-solving, 
          and I've already completed several IT projects, including the website you're currently viewing. 
          Outside of coding, I assist researchers with their data analysis needs.
        </p>
        <br>
        <p>
          I'm actively seeking opportunities as an intern or junior developer.
          If you or your organization has a suitable opening, I'd be delighted to hear from you.
        </p>
      </section>

      <!-- container for skills -->
      <section id="skills" class="skills">
        <div class="words">
          <span style="margin-bottom: 0.5em; font-size: 2em;">Jest</span>
          <span style="margin-top: 1em;">PostgreSQL</span>
          <span style="margin-top: 1.5em; font-size: 1.3em;">Django</span>
        </div>

        <div class="words">
          <span style="margin-top: 0em; font-size: 1.5em;">React</span>
          <h4 class="developer">DEVELOPER</h4>
          <span style="margin-top: 0.5em;">NodeJS</span>
        </div>

        <div class="words">
          <span style="margin-top: 0.2em; font-size: 1.7em;">Docker</span>
          <span style="margin-bottom: 0.5em;">Playwright</span>
          <span style="margin-top: 0.2em; font-size: 2em;">Mongo</span>
        </div>
      </section>

      <!-- container for education-->
      <section id="education" class="education">
        <h2><i class="fa-solid fa-graduation-cap" style="margin-right: 1em;"></i>Education</h2>
      </section>

      <!-- container for languages -->
      <aside class="languages">
        <div class="separate_language" style="background-color: #f1e8f8; box-shadow: none;">
          <h2>Languages</h2>
        </div>
                
        <div class="separate_language">
          <h2>Russian</h2>
          <p style="margin-bottom: 0.7em;">
            <i class="fa-solid fa-star" style="color: #ffd43b;"></i>
            <i class="fa-solid fa-star" style="color: #ffd43b;"></i>
            <i class="fa-solid fa-star" style="color: #ffd43b;"></i>
            <i class="fa-solid fa-star" style="color: #ffd43b;"></i>
            <i class="fa-solid fa-star" style="color: #ffd43b;"></i>
            <i class="fa-solid fa-star" style="color: #ffd43b;"></i>
          </p>
          <p>Native proficiency</p>
        </div>

        <div class="separate_language">
          <h2>English</h2>
          <p style="margin-bottom: 0.7em;">
            <i class="fa-solid fa-star" style="color: #ffd43b;"></i>
            <i class="fa-solid fa-star" style="color: #ffd43b;"></i>
            <i class="fa-solid fa-star" style="color: #ffd43b;"></i>
            <i class="fa-solid fa-star" style="color: #ffd43b;"></i>
            <i class="fa-solid fa-star" style="color: lightgrey;"></i>
            <i class="fa-solid fa-star" style="color: lightgrey;"></i>
          </p>
          <p>Professional working proficiency</p>
        </div>

        <div class="separate_language">
          <h2>Finnish</h2>
          <p style="margin-bottom: 0.7em;">
            <i class="fa-solid fa-star" style="color: #ffd43b;"></i>
            <i class="fa-solid fa-star" style="color: #ffd43b;"></i>
            <i class="fa-solid fa-star" style="color: #ffd43b;"></i>
            <i class="fa-solid fa-star" style="color: lightgrey;"></i>
            <i class="fa-solid fa-star" style="color: lightgrey;"></i>
            <i class="fa-solid fa-star" style="color: lightgrey;"></i>
          </p>
          <p>Working proficiency</p>
        </div>

      </aside>
      
      <section id="projects" class="header">
        <h2>Projects</h2>
      </section>

      <!-- container for projects -->
      <div class="projects">

        <!-- Project 1 -->
        <article class="project">

          <div class="screenshot">
            <a href="images/roomies.png" target="_blank">
              <img src="images/roomies.png" alt="Screenshot of the Roomies App booking interface">
            </a>
          </div>

          <div class="project-content">
            <h2 style="text-align: center;">Roomies App</h2>
            <p>
              A comprehensive booking system developed using modern web technologies: 
              JavaScript, NodeJS, Docker, Express, and PostgreSQL for the backend, 
              complemented by React, Vite, Redux, React Big Calendar, Styled Components for the frontend. 
              The application features Jest for backend testing, Playwright for end-to-end testing, 
              and maintains code quality through ESLint. This enterprise-level solution enables authenticated 
              users to book rooms in hotels or office buildings, with full administrative oversight. 
              The project served as my capstone submission for the University of Helsinki's Full Stack Open Course.
            </p>
          </div>
          <a href="https://roomies-frontend-fpzi.onrender.com/" class="project-button contact" target="_blank" style="width: auto; padding: 0 1em;">
            Visit Roomies App Website
          </a>
        </article>

        <!-- Project 2 -->  
        <article class="project">
          <div class="screenshot">
            <a href="images/selvavastaus.png" target="_blank">
              <img src="images/selvavastaus.png" alt="Screenshot of the Selvä Vastaus interface">
            </a>
          </div>

          <div class="project-content">
            <h2 style="text-align: center;">Selvä Vastaus</h2>
            <p>
              A linguistic search tool crafted with Python, utilizing Flask and SQLite for backend operations, 
              whilst employing HTML, Bootstrap and Jinja for an intuitive frontend interface. This specialized 
              application helps users locate Finnish words based on their suffixes, 
              offering valuable assistance for language learning and research. 
              I developed this as my final project for Harvard University's CS50x course.
            </p>
          </div>
          <a href="https://selvavastaus.onrender.com/" class="project-button contact" target="_blank" style="width: auto; padding: 0 1em;">
            Visit Selvä Vastaus Website
          </a>
        </article>

        <!-- Project 3 -->  
        <article class="project">
          <div class="screenshot">
            <a href="images/mainos.png" target="_blank">
              <img src="images/mainos.png" alt="Screenshot of Mainos">
            </a>
          </div>

          <div class="project-content">
            <h2 style="text-align: center;">Mainos</h2>
            <p>
              An interactive location tracking tool for Jyväskylä using JavaScript, Leaflet.js mapping library, 
              and IndexedDB for data persistence. This web application enables users to load campaign-specific 
              locations with unique color coding, track visit progress with automatic saving, and manage multiple 
              campaigns simultaneously. Features include marker clustering, city filtering, legend display, 
              and progress import/export functionality, providing a comprehensive solution for location-based 
              campaign management.
            </p>
          </div>
          <a href="https://romashkoyp.github.io/mainos/" class="project-button contact" target="_blank" style="width: auto; padding: 0 1em;">
            Visit Mainos Website
          </a>
        </article>

      </div>
    </main>

    <!-- Image Modal -->
    <div id="imageModal" class="modal">
      <div class="modal-content">
        <span class="close-modal">&times;</span>
        <img id="modalImg" src="images" alt="Enlarged image">
      </div>
    </div>

  <!-- standard footer -->
  <footer>
    <div>
      <a href="mailto:<EMAIL>"><i class="fa-solid fa-envelope"></i></a>
      <a href="https://github.com/romashkoyp" target="_blank"><i class="fa-brands fa-github"></i></a>
      <a href="https://www.linkedin.com/in/romashkoyp/" target="_blank"><i class="fa-brands fa-linkedin"></i></a>
    </div>
    <p style="text-align: center;">Yaroslav Romashko © 2025</p>
  </footer>
  </body>
</html>
