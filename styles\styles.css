/* inspired by this design https://www.behance.net/gallery/212417965/Personal-Portfolio-Web-UI-DesignConcept?tracking_source=search_projects|web+design+portfolio&l=103 */

:root {
  --transition-standard: 0.4s cubic-bezier(.25,.5,.25,1);
  --card-background:rgba(248, 248, 248);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  scroll-behavior: smooth;
  /* outline: 1px solid red !important; */
}

body {
  border-width: clamp(0.05rem, 0.8vw, 1rem);
  border-style: solid;
  border-color: #390082;
  background: #f1e8f8;
  font-family: "Poppins", sans-serif;
}

.container-body {
  width: 1280px;
  max-width: 90%;
  margin: auto;
}

.main-nav {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--card-background);
  padding: 0;
  width: 100%;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

:is(.nav-link, .nav-toggle) {
  display: flex;
  width: 7em;
  justify-content: center;
  align-items: center;
  text-decoration: none;
  color: black;
  font-size: 1.3em;
  padding: 1em 0;
  transition: all var(--transition-standard);
}

.nav-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
}

.nav-link:hover {
  background-color: #9747ff;
  color: white;
}

.nav-toggle:hover {
  background-color: #9747ff;
  color: white;
}

:is(h1, h2, h3, h4, h5, h6) {
  font-family: "Noto Serif", serif;
  font-weight: 800;
}

h1.name, h4.developer {
  background: linear-gradient(to right, #f82d96, #9747ff, #0075ff);
  -webkit-text-fill-color: transparent;
  background-clip: padding-box;
  -webkit-background-clip: text;
  font-family: "Russo One", sans-serif;
  font-weight: 500;
  font-size: 3em;
}

h2.title {
  font-family: "Russo One", sans-serif;
  font-size: 1.5em;
  font-weight: 100;
  margin-bottom: 1.5em;
}

h2 {
  margin-top: 1em;
  margin-bottom: 1em;
}

p {
  text-align: justify;
}

.main-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.title-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  order: 3;
}

.profile {
  display: flex;
  flex-direction: column;
  justify-content: left;
  align-items: center;
  width: 600px;
  max-width: 90%;
  margin: auto;
}

.skills {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 2em;
  margin-bottom: 2em;
  font-size: clamp(0.5rem, 2vw, 2rem);
}

.words {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.words span {
  font-size: 1.5em;
  font-weight: bold;
  margin: 1em;
}

.education {
  margin-top: 2em;
  margin-bottom: 2em;
  display: flex;
  flex-direction: column;
  border-radius: 10px;
  background-color: var(--card-background);
  padding: 1em 3em 3em 3em;
  box-shadow: 0 4px 12px rgba(57, 0, 130, 0.3);
  transition: all var(--transition-standard);
}

.arrow {
  width: 0.5em;
  height: 0.5em;
  border: solid black;
  border-width: 0 3px 3px 0;
  display: inline-block;
  padding: 4px;
  margin-left: 0.7em;
  transition: transform 0.4s ease;
  -webkit-transition: -webkit-transform 0.4s ease;
}

.right {
  transform: rotate(-45deg);
  -webkit-transform: rotate(-45deg);
}

.arrow.right.active {
  transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
}

:is(.education:hover, .separate_language:hover, .project:hover) {
  box-shadow: 0 8px 16px rgba(57, 0, 130, 0.5);
}

.education div {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: space-between;

}

.p-education {
  min-width: 8em;
  color: grey;
  font-size: 1.2em;
}

.course {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  color: grey;
  font-size: 1.1em;
}

.course-table {
  width: 100%;
  border-collapse: collapse;
  margin: 0.5em 0;
}

:is(.course-table th, .course-table td) {
  padding: 0.5em 0.5em;
  border-bottom: 1px solid #ddd;
  text-align: left;
}

.course-table th {
  background-color: rgba(151, 71, 255, 0.1);
  font-weight: 600;
}

.course-table tr:hover {
  background-color: rgba(151, 71, 255, 0.05);
  transition: all var(--transition-standard);
}

.fa-file-lines {
  color: lightgrey;
}

.fa-file-lines:hover {
  color: #9747ff;
  transition: all var(--transition-standard);
}

.certificate-link {
  position: relative;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.certificate-link .tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: black;
  color: white;
  padding: 6px 10px;
  border-radius: 5px;
  font-size: 14px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-standard);
  margin-bottom: 8px;
  z-index: 100;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.certificate-link .tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 5px;
  border-style: solid;
  border-color: black transparent transparent transparent;
}

.certificate-link:hover .tooltip {
  opacity: 1;
  visibility: visible;
  bottom: calc(100% + 5px);
}

.sortable {
  cursor: pointer;
  position: relative;
}

.languages {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-evenly;
}

.separate_language {
  background-color: var(--card-background);
  border-radius: 10px;
  padding: 0.5em 1em 1em 1em;
  flex-grow: 0;
  flex-shrink: 1;
  flex-basis: 22%;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(57, 0, 130, 0.3);
  transition: all var(--transition-standard);
}

.separate_language p {
  text-align: left;
}

hr {
  margin: auto;
  width: 90%;
}

.header {
  display: flex;
  flex-direction: column;
  width: 100%;
  align-items: center;
  margin-top: 2em;
}

.projects {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
}

.project {
  display: flex;
  flex-grow: 0;
  flex-shrink: 1;
  flex-basis: 48.5%;
  flex-direction: column;
  overflow: hidden;
  margin: 1em 0;
  background-color: var(--card-background);
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(57, 0, 130, 0.3);
  transition: all var(--transition-standard);
}

.project-content {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding: 1em 2em;
  margin: auto;
  flex: 1;
}

.project-button {
  margin: 3em auto;
}

.contact {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  font-weight: 400;
  border: 2px solid transparent;
  border-radius: 1em;
  cursor: pointer;
  height: 2.1em;
  width: 10em;
  transition: all var(--transition-standard);
  background-color: #9747ff;
  color: white;
  text-decoration: none;
  overflow: hidden;
}

.screenshot {
  position: relative;
  padding-top: 48%;
  overflow: hidden;
  border-radius: 10px;
}

.project img{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  transition: all var(--transition-standard);
}

.project img:hover {
  transform: scale(1.03);
}

.desktop-button {
  margin-top: 3em;
}

.contact:focus {
  outline: none;
}

.contact:hover {
  background-color: #be9cea;
}

.contact i {
  margin-right: 0.5em;
}

.mobile-button {
  display: none;
}

.photo-frame {
  order: 2;
  width: auto;
  height: auto;
}

.photo-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 25%;
}

.photo {
  height: auto;
  object-fit: cover;
  width: clamp(8rem, 25vw, 25rem);
  filter: grayscale(100%);
}

.bracket-left {
  order: 1;
  left: 0;
  font-size: clamp(9rem, 28vw, 28rem);
  font-family: monospace;
  color: transparent;
  background: linear-gradient(45deg, #ff00ff, #0000ff);
  background-clip: padding-box;
  -webkit-background-clip: text;
}

.bracket-right {
  order: 4;
  right: 0;
  font-size: clamp(9rem, 28vw, 28rem);
  font-family: monospace;
  color: transparent;
  background: linear-gradient(45deg, #0000ff, #ff00ff);
  background-clip: padding-box;
  -webkit-background-clip: text;
}

footer {
  width: 100%;
  text-align: center;
}

footer div {
  display: flex;
  align-content: center;
  justify-content: center;
  gap: 2em;
  font-size: 2em;
  margin: 2em 0 1em 0;
}

footer a {
  transition: all var(--transition-standard);
  color: grey;
}

footer a:hover {
  color: #390082;
}

.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  padding-top: 50px;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(5px);
}

.modal-content {
  position: relative;
  margin: auto;
  padding: 0;
  width: 90%;
  max-width: 1200px;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.close-modal {
  position: absolute;
  top: -20px;
  right: -20px;
  color: white;
  background-color: black;
  font-size: 40px;
  font-weight: bold;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid white;
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 0.6;
  transition: all var(--transition-standard);
}

.close-modal:hover {
  transform: rotate(90deg);
}

#modalImg {
  width: 100%;
  max-height: 90vh;
  object-fit: contain;
}

.no-scroll {
  overflow: hidden;
}

@media (max-width: 1100px) {
  .languages div {
    flex-basis: 45%;
    margin: 0.5em 0;
  }
  .project {
    flex-basis: 100%;
  }
}

@media screen and (max-width: 999px) {
  .main-container {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
  }

  .title-container {
    order: 1;
    width: 100%;
  }

  .main-container::after {
    content: "";
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    width: 100%;
    order: 2;
  }
 
  .bracket-left {
    order: 2;
  }
  
  .photo-frame {
    order: 2;
  }
  
  .bracket-right {
    order: 2;
  }

  .desktop-button {
    display: none;
  }
  
  .mobile-button {
    order: 3;
    display: flex;
    justify-content: center;
    margin-top: 2.5em;
    margin-bottom: 1.5em;
  }

  .education div {
    flex-direction: column;
  }

  .p-education {
    margin: 0.3em 0;
  }

}

@media screen and (max-width: 600px) {
  .main-nav {
    flex-direction: column;
  }

  .nav-list {
    display: none;
    flex-direction: column;
    width: 100%;
  }

  .nav-link {
    width: 100%;
  }

  .nav-toggle {
    display: flex;
    width: 100%;
    height: 3em;
    text-align: center;
    color: inherit;
  }

  .main-nav.responsive .nav-list {
    display: flex;
  }

  .languages div {
    flex-basis: 100%;
    margin: 0.5em 0;
  }

  .education {
    padding: 1em;
  }

  p {
    font-size: 0.87em;
  }
}
